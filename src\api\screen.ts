import request from '/@/utils/request';

/**
 * @method getSpeciesFrequency 获取设备点位物种频次
 */

//
export function getSpeciesFrequency(params?: { timeType?: TimeType; deviceList?: string[] }) {
	return request({
		url: '/dashboard/devicePoint/species/frequency',
		method: 'get',
		params,
	});
}

export function getDeviceRank(params: any) {
	return request({
		url: '/dashboard/device/rank/page',
		method: 'get',
		params,
	});
}

export function getSpeciesRank(params: any) {
	return request({
		url: '/dashboard/species/rank/page',
		method: 'get',
		params,
	});
}

// 查询对照表物种 用于人工鉴定
export function getSpecies() {
	return request({
		url: `/monitor-events/directory/species`,
		method: 'get',
	});
}

//  查询存在物种  用于页面查询
export function getExistSpecies() {
	return request({
		url: `/monitor-events/exist/directory/species`,
		method: 'get',
	});
}
