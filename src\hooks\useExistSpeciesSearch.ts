import { ref, onBeforeMount, onBeforeUnmount } from 'vue';
import { useExistSpeciesStore } from '/@/stores/existSpecies';

export interface Species {
	name: string;
	alias?: string;
	[key: string]: any;
}

export interface UseExistSpeciesSearchOptions {
	/**
	 * 是否启用自动刷新功能
	 * 当设置为 true 时，hook 会在组件挂载时自动启动物种数据的定时刷新，
	 * 并在组件卸载时自动停止刷新
	 * @default false
	 */
	autoRefresh?: boolean;
}

/**
 * 已存在物种搜索 Hook
 * 提供已人工鉴定存在物种的联想搜索功能，并可选择性地管理自动刷新
 * 用于页面查询已确认存在的物种
 *
 * @param options 配置选项
 * @param options.autoRefresh 是否启用自动刷新功能，默认为 true
 * @推荐使用 searchExistSpecies 方法，它利用智能缓存提供更好的性能
 *
 * @example
 * // 基础用法（仅搜索功能）
 * const { speciesOptions, searchExistSpecies } = useExistSpeciesSearch();
 *
 * @example
 * // 启用自动刷新功能
 * const { speciesOptions, searchExistSpecies } = useExistSpeciesSearch({ autoRefresh: true });
 */
export function useExistSpeciesSearch(options: UseExistSpeciesSearchOptions = {}) {
	const { autoRefresh = true } = options;
	const speciesOptions = ref<Species[]>([]);
	const searchLoading = ref(false);
	const searchError = ref<string | null>(null);

	// 获取 store 实例
	const existSpeciesStore = useExistSpeciesStore();

	// 自动刷新管理
	if (autoRefresh) {
		onBeforeMount(() => {
			console.log('useExistSpeciesSearch: 启动物种数据自动刷新');
			existSpeciesStore.startAutoRefresh();
		});

		onBeforeUnmount(() => {
			console.log('useExistSpeciesSearch: 停止物种数据自动刷新');
			existSpeciesStore.stopAutoRefresh();
		});
	}

	/**
	 * 基于 existSpecies store 的物种搜索
	 * @param name 物种名称关键字
	 * @param eventTypeOrTypes 事件类型 - 可以是单个值、数组，或不传
	 * @returns Promise<Species[]> 搜索结果
	 */
	const searchExistSpecies = async (
		name: string,
		eventTypeOrTypes?: number | string | (number | string)[]
	): Promise<Species[]> => {
		if (!name || name.trim().length === 0) {
			return [];
		}

		searchLoading.value = true;
		searchError.value = null;

		try {
			// 确保数据存在（智能缓存）
			await existSpeciesStore.ensureSpecies();

			// 使用 store 的过滤方法
			const results = existSpeciesStore.getFilteredSpecies(name, eventTypeOrTypes);

			console.log('[ results ] >', results);
			// 更新选项列表
			speciesOptions.value = results;

			return results;
		} catch (error) {
			console.error('搜索存在物种失败:', error);
			searchError.value = '搜索存在物种失败';
			speciesOptions.value = [];
			return [];
		} finally {
			searchLoading.value = false;
		}
	};

	/**
	 * 清空搜索结果
	 */
	const clearSearchResults = (): void => {
		speciesOptions.value = [];
		searchError.value = null;
	};

	/**
	 * 重置搜索状态
	 */
	const resetSearchState = (): void => {
		speciesOptions.value = [];
		searchLoading.value = false;
		searchError.value = null;
	};

	return {
		// 状态
		speciesOptions,
		searchLoading,
		searchError,

		// 方法
		searchExistSpecies, // 推荐：利用智能缓存的搜索方法
		clearSearchResults,
		resetSearchState,

		// 自动刷新相关（仅在启用时有意义）
		isAutoRefreshEnabled: autoRefresh,
		// 提供手动控制自动刷新的方法（可选）
		startAutoRefresh: () => existSpeciesStore.startAutoRefresh(),
		stopAutoRefresh: () => existSpeciesStore.stopAutoRefresh(),
	};
}
