import { ref, computed, onUnmounted } from 'vue';
import { getSpecies } from '/@/api/screen';

export interface Species {
	name: string;
	alias?: string;
	[key: string]: any;
}

/**
 * 物种搜索 Hook
 * 提供全部物种的搜索功能，用于人工鉴定
 * 基于对照表物种数据进行本地搜索和过滤
 *
 * 功能特性：
 * - 智能缓存机制（默认1分钟过期）
 * - 可配置的定时自动刷新
 * - 本地搜索索引优化
 * - 组件卸载时自动清理定时器
 *
 * @example
 * const {
 *   searchSpecies,
 *   startAutoRefresh,
 *   stopAutoRefresh,
 *   isAutoRefreshing
 * } = useSpeciesSearch();
 *
 * // 启动自动刷新（使用默认间隔）
 * startAutoRefresh();
 *
 * // 启动自动刷新（自定义间隔：30秒）
 * startAutoRefresh(30 * 1000);
 */
export function useSpeciesSearch() {
	// 状态管理
	const allSpecies = ref<Species[]>([]);
	const searchLoading = ref(false);
	const searchError = ref<string | null>(null);
	const lastFetchTime = ref(0);
	const cacheExpireTime = ref(1 * 60 * 1000); // 1分钟缓存过期时间
	const refreshTimer = ref<number | null>(null); // 定时器ID

	const searchCache = ref<Map<string, Species[]>>(new Map());
	const searchIndex = ref<Map<string, Species[]>>(new Map());

	// 计算属性：判断缓存是否过期
	const isCacheExpired = computed(() => {
		const now = Date.now();
		return now - lastFetchTime.value > cacheExpireTime.value;
	});

	// 计算属性：判断是否有有效数据
	const hasValidData = computed(() => {
		return allSpecies.value.length > 0;
	});

	/**
	 * 构建搜索索引以提高搜索性能
	 */
	const buildSearchIndex = () => {
		const index = new Map<string, Species[]>();

		allSpecies.value.forEach((species) => {
			// 按名称首字符建立索引
			if (species.name) {
				const firstChar = species.name.charAt(0).toLowerCase();
				if (!index.has(firstChar)) {
					index.set(firstChar, []);
				}
				index.get(firstChar)!.push(species);
			}

			// 按别名首字符建立索引
			if (species.alias) {
				const firstChar = species.alias.charAt(0).toLowerCase();
				if (!index.has(firstChar)) {
					index.set(firstChar, []);
				}
				index.get(firstChar)!.push(species);
			}
		});

		searchIndex.value = index;
		console.log(`搜索索引构建完成，共 ${index.size} 个索引键`);
	};

	/**
	 * 获取全部物种数据
	 * @returns Promise<Species[]> 全部物种列表
	 */
	const fetchAllSpecies = async (): Promise<Species[]> => {
		searchLoading.value = true;
		searchError.value = null;

		try {
			const response = await getSpecies();
			const results = response.payload || [];

			// 更新缓存数据
			allSpecies.value = results;
			lastFetchTime.value = Date.now();

			// 构建搜索索引
			buildSearchIndex();

			// 清空搜索缓存
			searchCache.value.clear();

			console.log(`物种对照表数据已更新，共 ${results.length} 条记录`);
			return results;
		} catch (error) {
			console.error('获取物种对照表失败:', error);
			searchError.value = '获取物种对照表失败';
			allSpecies.value = [];
			return [];
		} finally {
			searchLoading.value = false;
		}
	};

	/**
	 * 确保物种数据存在（智能缓存）
	 * 如果没有数据或缓存过期，则自动获取
	 */
	const ensureSpeciesData = async (): Promise<void> => {
		// 如果正在加载，直接返回
		if (searchLoading.value) {
			return;
		}

		// 如果有有效数据且缓存未过期，直接返回
		if (hasValidData.value && !isCacheExpired.value) {
			return;
		}

		// 获取数据
		await fetchAllSpecies();
	};

	/**
	 * 搜索物种（本地搜索）
	 * @param name 物种名称关键字
	 * @returns Promise<Species[]> 搜索结果
	 *
	 * @example
	 * // 基础搜索
	 * searchSpecies('老虎')
	 * searchSpecies('鸟')
	 */
	const searchSpecies = async (name: string): Promise<Species[]> => {
		if (!name || name.trim().length === 0) {
			return [];
		}

		console.log('[ name', name);

		// 确保数据存在
		await ensureSpeciesData();

		// 本地搜索过滤
		const searchTerm = name.trim().toLowerCase();
		const filteredResults = allSpecies.value.filter((species) => {
			// 按名称搜索
			const nameMatch = species.name?.toLowerCase().includes(searchTerm);
			const aliasMatch = species.alias?.toLowerCase().includes(searchTerm);

			// console.log('[ nameMatch ] >', nameMatch, aliasMatch);
			return nameMatch || aliasMatch;
		});

		return filteredResults;
	};

	/**
	 * 获取所有物种名称列表（去重）
	 * @returns Promise<string[]> 物种名称列表
	 */
	const getAllSpeciesNames = async (): Promise<string[]> => {
		await ensureSpeciesData();

		const names = allSpecies.value.map((item) => item.name).filter(Boolean);

		return [...new Set(names)];
	};

	/**
	 * 清空搜索结果和缓存
	 */
	const clearCache = (): void => {
		stopAutoRefresh(); // 停止定时器
		allSpecies.value = [];
		searchError.value = null;
		lastFetchTime.value = 0;
		searchCache.value.clear();
		searchIndex.value.clear();
	};

	/**
	 * 重置搜索状态
	 */
	const resetSearchState = (): void => {
		searchLoading.value = false;
		searchError.value = null;
	};

	/**
	 * 强制刷新数据
	 */
	const refreshData = async (): Promise<Species[]> => {
		return await fetchAllSpecies();
	};

	/**
	 * 启动自动刷新
	 * @param intervalMs 刷新间隔（毫秒），默认使用缓存过期时间
	 */
	const startAutoRefresh = (intervalMs?: number): void => {
		// 如果已经有定时器在运行，先清除
		stopAutoRefresh();

		// 使用传入的间隔时间或默认的缓存过期时间
		const interval = intervalMs || cacheExpireTime.value;

		// 立即获取一次数据
		ensureSpeciesData();

		// 设置定时器
		refreshTimer.value = window.setInterval(() => {
			console.log('定时刷新物种对照表数据...');
			fetchAllSpecies();
		}, interval);

		console.log(`物种对照表自动刷新已启动（${interval / 1000}秒间隔）`);
	};

	/**
	 * 停止自动刷新
	 */
	const stopAutoRefresh = (): void => {
		if (refreshTimer.value !== null) {
			clearInterval(refreshTimer.value);
			refreshTimer.value = null;
			console.log('物种对照表自动刷新已停止');
		}
	};

	/**
	 * 设置缓存过期时间
	 * @param milliseconds 缓存过期时间（毫秒）
	 */
	const setCacheExpireTime = (milliseconds: number): void => {
		cacheExpireTime.value = milliseconds;

		// 如果正在自动刷新，重新启动以应用新的间隔时间
		if (refreshTimer.value !== null) {
			startAutoRefresh();
		}
	};

	// 组件卸载时清理定时器
	onUnmounted(() => {
		stopAutoRefresh();
	});

	return {
		// 状态
		allSpecies: computed(() => allSpecies.value),
		searchLoading: computed(() => searchLoading.value),
		searchError: computed(() => searchError.value),
		hasValidData,
		isCacheExpired,
		cacheExpireTime: computed(() => cacheExpireTime.value),
		isAutoRefreshing: computed(() => refreshTimer.value !== null),

		// 方法
		searchSpecies,
		fetchAllSpecies,
		ensureSpeciesData,
		getAllSpeciesNames,
		clearCache,
		resetSearchState,
		refreshData,

		// 定时刷新相关方法
		startAutoRefresh,
		stopAutoRefresh,
		setCacheExpireTime,

		// 兼容性别名（为了保持与 useExistSpeciesSearch 的接口一致）
		speciesOptions: computed(() => allSpecies.value),
		searchExistSpecies: searchSpecies,
		clearSearchResults: clearCache,
	};
}
