<template>
	<div class="detail-page-container">
		<div class="detail-page-title">
			<img
				class="back"
				@click="onToMore"
				src="/src/assets/sd/data-screen/more.png"
				alt=""
			/><Breadcrumb />
		</div>

		<div class="detail-page-filters">
			<div class="filter-group">
				<Select
					class="mr20"
					v-model="deviceValue"
					:options="deviceList"
					multiple
					filterable
					key-field="num"
					label-field="name"
					value-field="num"
					placeholder="请选择或输入设备"
					@change="handleDeviceChange"
				/>
				<Select
					class="mr20"
					v-model="speciesEventType"
					:options="animalTypesStore.screenAnimalTypeOptions"
					placeholder="请选择物种类型"
					:clearable="false"
					@change="handleEventTypeChange"
				/>
				<Select
					class="mr20"
					v-model="animalNameValue"
					:options="speciesOptions"
					key-field="name"
					label-field="name"
					value-field="name"
					placeholder="请输入选择物种"
					filterable
					@change="handleAnimalNameChange"
					:on-search="handleSpeciesSearch"
				/>
				<DatePicker
					v-model:startTime="state.tableData.filter.startTime"
					v-model:endTime="state.tableData.filter.endTime"
					@change="handleDateChange"
				/>
			</div>
			<div class="btn-group">
				<div class="hlj-btn" @click="handleSearch">搜索</div>
				<div class="hlj-btn" @click="handleReset">重置</div>
			</div>
		</div>

		<custom-scrollbar
			v-loading="state.tableData.loading"
			ref="scroll"
			contentClass="species"
			:style="{ height: '100%' }"
			wrapperClass="wrapperClass detail-page-content detail-page-loading"
			:autoHide="false"
		>
			<div
				v-if="state.tableData.data.length > 0"
				class="species-item animate__animated"
				v-for="(item, $index) in state.tableData.data"
				:key="item.id"
			>
				<div class="picture">
					<el-image
						:src="
							item.pictureThumbnailUrl ||
							item.pictureUrl ||
							item.oriPictureThumbnailUrl ||
							item.oriPictureUrl ||
							''
						"
						:initial-index="$index"
						:preview-teleported="true"
						title="查看大图"
						@click="handleImageShow(item, $index)"
					>
						<template #placeholder>
							<div class="image-placeholder">
								<el-icon class="is-loading">
									<ele-Loading />
								</el-icon>
							</div>
						</template>
						<template #error>
							<div class="load-error">
								<img
									class="small-img-error"
									src="/src/assets/fullScreen/small-load-error.png"
									title="加载失败"
									alt=""
								/>
							</div>
						</template>
					</el-image>
					<div class="recResult-content">
						<div class="name">
							<span v-for="res in item.monitorEventDetails" :key="res.recResult">
								{{ res.recResult }}{{ res.recResultCnt && `（${res.recResultCnt}）` }}
							</span>
						</div>
					</div>
				</div>
				<div class="other-content">
					<div>
						<el-tooltip :hide-after="0">
							设备名称：{{ item.device.name
							}}{{ item.device.channelName ? ` [${item.device.channelName}]` : '' }}
							<template #content
								>{{ item.device.name
								}}{{ item.device.channelName ? ` [${item.device.channelName}]` : '' }}
							</template>
						</el-tooltip>
					</div>
					<div class="mt5">
						<el-tooltip :disabled="!item.longitude" :hide-after="0">
							GPS：{{
								item.longitude && item.latitude ? `${item.longitude},${item.latitude}` : '-'
							}}
							<template #content>
								{{ item.longitude && item.latitude ? `${item.longitude},${item.latitude}` : '-' }}
							</template>
						</el-tooltip>
					</div>
					<div class="mt5">时间：{{ item.recTime }}</div>
				</div>
			</div>
			<div v-else class="no-data">暂无数据</div>
		</custom-scrollbar>

		<div class="detail-page-pagination">
			<el-pagination
				v-if="state.tableData.total > 0"
				v-model:current-page="state.tableData.pageParams.page"
				:page-sizes="[21, 42]"
				background
				:teleported="false"
				v-model:page-size="state.tableData.pageParams.size"
				layout="total, sizes, prev, pager, next, jumper"
				:total="state.tableData.total"
				@size-change="onHandleSizeChange"
				@current-change="onHandleCurrentChange"
			>
			</el-pagination>
		</div>
		<ImgPreview
			ref="imgPreviewRef"
			:perviewPicList="perviewPicList"
			:pageSize="state.tableData.pageParams.size"
			:currentPage="state.tableData.pageParams.page"
			:total="state.tableData.total"
			:loading="state.tableData.loading"
			:data="state.tableData.data"
			@loadNextPage="handleLoadNextPage"
			@loadPrevPage="handleLoadPrevPage"
		/>
	</div>
</template>

<script setup lang="ts" name="DataScreenAtlas">
import { ref, reactive, onBeforeMount, computed } from 'vue';
import CustomScrollbar from 'custom-vue-scrollbar';
import Select from '/@/screen/component/select/index.vue';
import DatePicker from '/@/screen/component/datePicker/index.vue';
import { useRouter, useRoute } from 'vue-router';
import { calculateTimeRange } from '/@/utils/timeRange';
import 'custom-vue-scrollbar/dist/style.css';
import { getDevices } from '/@/api/devices';
import { createQuery } from '/@/screen/utils/query';
import { useAnimalTypesStore } from '/@/stores/animalTypes';
import { useExistSpeciesSearch } from '/@/hooks/useExistSpeciesSearch';
import Breadcrumb from '/@/screen/component/breadcrumb/index.vue';
import ImgPreview from '/@/components/ImgPreview.vue';
import { getEventTypes } from '/@/api/monitorEvents';
// 设备类型定义
interface Device {
	id: string;
	name: string;
	num: string;
	[key: string]: any;
}
// 常量定义
const ALL_SPECIES_TYPE = 'all';
const speciesEventType = ref<string | number>(ALL_SPECIES_TYPE);
const deviceValue = ref<string[]>([]);
const deviceList = ref<Device[]>([]);
const animalNameValue = ref<string>('');
const router = useRouter();
const route = useRoute();

// 使用动物类型 store
const animalTypesStore = useAnimalTypesStore();

// 使用已存在物种搜索 hook（启用自动刷新功能）
const { speciesOptions, searchExistSpecies, clearSearchResults } = useExistSpeciesSearch({
	autoRefresh: true,
});

const onToMore = () => {
	router.back();
};
const state = reactive<ViewBaseState<MonitorEventRow>>({
	tableData: {
		filter: {
			groupEventType: 0, // 生物多样性
			sort: 'recTime,desc',
			startTime: '',
			endTime: '',
		},
		data: [],
		loading: false,
		pageParams: {
			page: 1,
			size: 21,
		},
		total: 0,
	},
});
// # 大图预览
const perviewPicList = computed(() => {
	return state.tableData.data.map((item) => item.pictureUrl || item.oriPictureUrl);
});

const handleEventTypeChange = (val: any) => {
	speciesEventType.value = val;
	animalNameValue.value = '';
	// 清空搜索结果
	clearSearchResults();
};

const getDeviceList = async () => {
	try {
		const { payload } = await getDevices({ typeFilter: 10 });
		deviceList.value = payload || [];
	} catch (error) {
		console.error('获取设备列表失败:', error);
		deviceList.value = [];
	}
};

const handleDeviceChange = (val: any) => {
	deviceValue.value = val;
};
const handleAnimalNameChange = (val: any) => {
	animalNameValue.value = val;
};

/**
 * 物种搜索处理函数
 * 根据当前选择的物种事件类型进行物种搜索
 * @param name - 物种名称关键字
 * @returns Promise<Species[]> 搜索结果
 */
const handleSpeciesSearch = async (name: string) => {
	if (!name || name.trim().length === 0) {
		return [];
	}

	try {
		const eventType =
			speciesEventType.value === ALL_SPECIES_TYPE ? undefined : speciesEventType.value;

		return await searchExistSpecies(name.trim(), eventType);
	} catch (error) {
		console.error('物种搜索失败:', error);
		// 发生错误时返回空数组，避免组件崩溃
		return [];
	}
};

const handleDateChange = (val: { startTime: string; endTime: string }) => {
	// getTableData();
};

const getTableData = async () => {
	state.tableData.data = [];
	state.tableData.loading = true;

	const query = createQuery({
		deviceNums: deviceValue.value,
		speciesEventTypes: [speciesEventType.value === ALL_SPECIES_TYPE ? 102 : speciesEventType.value],
		name: animalNameValue.value,
		page: state.tableData.pageParams.page - 1,
		size: state.tableData.pageParams.size,
		...state.tableData.filter,
	});

	// 模型类型、monitorEvent的事件类型 0:野生动物 1:鸟类 2:植物 5:烟火 20:高温 21:水生物种
	const { payload } = await getEventTypes(query);

	state.tableData.loading = false;
	state.tableData.data = payload.content;
	state.tableData.total = payload.totalElements;
};

// 分页改变
const onHandleSizeChange = (val: number) => {
	state.tableData.pageParams.size = val;
	getTableData();
};
const onHandleCurrentChange = (val: number) => {
	state.tableData.pageParams.page = val;
	getTableData();
};

const handleSearch = () => {
	getTableData();
};
const handleReset = () => {
	deviceValue.value = [];
	// 重置时默认选中"全部"物种类型，确保用户能看到所有数据
	speciesEventType.value = ALL_SPECIES_TYPE;
	animalNameValue.value = '';
	state.tableData.filter.startTime = '';
	state.tableData.filter.endTime = '';
	state.tableData.pageParams.page = 1;
	state.tableData.pageParams.size = 21;
	state.tableData.data = [];
	state.tableData.total = 0;
	getTableData();
};

const imgPreviewRef = ref<InstanceType<typeof ImgPreview>>();
const handleImageShow = (item: any, index: number) => {
	imgPreviewRef.value?.handlePreview(index);
};
// 加载下一页
const handleLoadNextPage = async () => {
	state.tableData.pageParams.page += 1;
	getTableData();
	console.log('加载下一页');
};

// 加载上一页
const handleLoadPrevPage = async () => {
	state.tableData.pageParams.page -= 1;
	getTableData();
};

onBeforeMount(async () => {
	// 确保动物类型数据存在（智能缓存）
	await animalTypesStore.ensureAnimalTypes();

	getDeviceList();

	console.log(route.query);

	// 处理事件类型参数
	if (route.query?.eventType) {
		if (route.query.eventType !== ALL_SPECIES_TYPE) {
			speciesEventType.value = Number(route.query.eventType);
		} else {
			speciesEventType.value = route.query.eventType;
		}
	}

	// 处理设备参数
	if (route.query?.deviceNum) {
		deviceValue.value = [route.query.deviceNum as string];
	}

	// 处理物种别名参数
	if (route.query?.speciesAlias) {
		animalNameValue.value = route.query.speciesAlias as string;
	} else {
		animalNameValue.value = '';
	}

	// 处理时间类型参数 - 参考rankDetail页面的实现
	const timeTypeParam = route.query.timeType;
	if (timeTypeParam) {
		// 将字符串转换为数字类型
		const timeType =
			timeTypeParam === ALL_SPECIES_TYPE ? ALL_SPECIES_TYPE : (Number(timeTypeParam) as TimeType);

		const { startDateTime, endDateTime } = calculateTimeRange(timeType);

		// 自动设置时间范围到DatePicker组件
		state.tableData.filter.startTime = startDateTime;
		state.tableData.filter.endTime = endDateTime;
	}

	console.log('animalNameValue.value', animalNameValue.value);
	getTableData();
});

// 组件卸载时的清理工作现在由 useExistSpeciesSearch hook 自动处理
</script>

<style lang="scss" scoped>
/* @import url(); 引入css类 */
@import '/src/theme/mixins/index.scss';
@import '/@/screen/styles/dataScreen.scss';
@import '/@/screen/styles/detailPage.scss';

:deep(.el-select) {
	.el-select__tags,
	.select-trigger,
	.el-input,
	.el-input__wrapper {
		height: 100%;
	}
}
:deep(.species) {
	display: flex;
	flex-wrap: wrap;
	box-sizing: border-box;

	.species-item {
		display: flex;
		flex-direction: column;
		width: vw(245);
		height: vh(245);
		margin-right: vw(20.8);
		margin-bottom: vw(20);
		border-radius: 5px;
		overflow: hidden;

		&:nth-child(7n) {
			margin-right: 0;
		}
		&:nth-last-child(-n + 7) {
			margin-bottom: 0;
		}

		.picture {
			position: relative;
			width: 100%;
			height: 60%;

			.el-image {
				width: 100%;
				height: 100%;
				cursor: pointer;
				transition: transform 0.2s;
			}

			.el-image:hover {
				transform: scale(1.02);
			}

			.recResult-content {
				width: 100%;
				position: absolute;
				left: 0;
				bottom: 0;
				background-color: rgba(0, 0, 0, 0.3);
				color: var(--el-color-white);
				padding: 5px;
			}
		}

		.other-content {
			padding: 10px;
			position: relative;
			background-color: rgba(33, 227, 221, 0.06);

			& > div {
				overflow: hidden;
				white-space: nowrap;
				text-overflow: ellipsis;
			}

			.operate-btns {
				position: absolute;
				right: 5px;
				top: 8px;

				.el-button.is-text {
					height: 22px;
					padding: 2px;
					margin-left: 4px !important;
				}

				.el-button.is-text .el-icon {
					font-size: 18px !important;
				}
			}

			.rb-icon {
				position: absolute;
				right: 10px;
				bottom: 10px;
				width: 16px;
				height: 16px;
				color: var(--el-color-warning);
			}
		}
	}
}

// 修改滚动条样式。
:deep(.scrollbar__thumb) {
	background-color: $sd-screen-success;
}

:deep(.wrapperClass) {
	height: calc(100% - (vh(40) + 40px) - vh(110));
	.species {
		height: 100%;
	}
}

:deep(.el-radio) {
	margin-right: 40px;
	.el-radio__label {
		color: rgba(255, 255, 255, 1);
		font-size: vw(18);
		font-family: 'AliMaMaShuHeiTi';
	}
	.el-radio__inner {
		width: 16px;
		height: 16px;
		background-color: transparent !important;
		&:hover {
			border-color: $radio-color;
		}
	}
	.is-checked {
		.el-radio__inner {
			width: 16px;
			height: 16px;
			border-color: #fff;
			border-width: vw(3);
			background-color: $radio-color !important;
		}
	}
	.el-radio__input.is-checked + .el-radio__label {
		color: $radio-color !important;
	}
	.el-radio__inner::after {
		display: none;
	}
}

// atlas 页面特有的分页样式调整
:deep(.el-pagination) {
	margin-top: vh(20);

	.is-active {
		&::before {
			background-color: rgba(40, 112, 60, 0.6) !important;
		}
	}
}
</style>
