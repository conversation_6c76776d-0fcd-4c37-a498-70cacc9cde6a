<template>
	<el-dialog
		:title="state.dialog.title"
		v-model="state.dialog.isShowDialog"
		fullscreen
		align-center
		class="ME-edit-dialog"
		:before-close="closeDialog"
	>
		<div class="ME-container">
			<div class="ME-center-canvas">
				<el-alert
					title="说明：① 鼠标滚轮放大缩小；② 鼠标右键按下拖拽图片; ③鼠标左键按下添加标注; ④ 鼠标左键选中标注框后修改; ⑤ 鼠标左键选中标注框后按Delete进行删除。"
					type="info"
					:closable="false"
					class="mb15"
				/>
				<div class="canvas-container" v-loading="state.imageLoading">
					<canvas
						id="canvasId"
						ref="canvasRef"
						@wheel="onWheelEvent"
						@mousedown="onMousedownEvent"
						@contextmenu.prevent
					></canvas>
				</div>
			</div>
			<div class="ME-right-result">
				<div class="first-level-title font16 mb15">识别结果</div>
				<!-- 数据加载提示 -->
				<el-form label-position="top">
					<template v-if="state.MEResult.length > 0">
						<el-form-item
							v-for="(item, $index) in state.MEResult"
							:key="$index"
							:label="`BOX${$index + 1}`"
						>
							<el-select
								v-model="item.name"
								filterable
								reserve-keyword
								:placeholder="dataLoading ? '正在加载物种数据...' : '请输入物种名称'"
								:filter-method="handleLocalFilter"
								class="flex-auto"
								clearable
								:disabled="state.activeResult !== $index"
								:loading="dataLoading"
								@change="(value: string) => handleSpeciesChange(value, $index)"
							>
								<el-option
									v-for="option in displayOptions"
									:key="option.value"
									:label="option.label"
									:value="option.value"
								/>
							</el-select>
							<el-input-number
								class="ml10"
								style="width: 120px"
								v-model="item.count"
								placeholder="数量"
								:min="1"
								:disabled="state.activeResult !== $index"
							/>
							<el-link class="ml10 mr10" type="info" @click="onDelete($index)">
								<el-icon :size="18"><ele-Delete /></el-icon>
							</el-link>
						</el-form-item>
					</template>
					<el-form-item v-else>
						<el-empty style="width: 100%"></el-empty>
					</el-form-item>
					<el-form-item>
						<el-button type="primary" @click="onConfirm">确定</el-button>
						<el-button @click="onResetMEResult">重置</el-button>
					</el-form-item>
				</el-form>
			</div>
		</div>
	</el-dialog>
</template>

<script setup lang="ts" name="MonitorEventEdit">
import { ref, reactive, nextTick, computed } from 'vue';
import { ElMessage } from 'element-plus';
import { updateMonitorEvent, MonitorEventDetail } from '/@/api/monitorEvents';
import { useAnimalTypesStore } from '/@/stores/animalTypes';
import { useSpeciesSearch } from '/@/hooks/useSpeciesSearch';
import mittBus from '/@/utils/mitt';

const animalTypesStore = useAnimalTypesStore();
// 使用物种搜索 hook
const { speciesOptions, ensureSpeciesData } = useSpeciesSearch();

// 过滤关键词
const filterKeyword = ref('');

// 数据加载状态
const dataLoading = ref(false);

// 是否有输入状态
const hasInput = ref(false);

// 统一的选项列表（合并物种和动物类型）
const allOptions = computed(() => {
	const options: Array<{
		label: string;
		value: string;
		type: string;
		eventType?: number;
		alias?: string;
	}> = [];

	// 添加物种选项
	speciesOptions.value.forEach((species) => {
		options.push({
			label: species.name,
			value: species.name,
			type: 'species',
			eventType: species.eventType,
			alias: species.alias,
		});
	});

	// 添加动物类型选项（排除"全部"）
	animalTypesStore.animalTypeOptions
		.filter((item) => item.label !== '全部')
		.forEach((animalType) => {
			options.push({
				label: animalType.label,
				value: animalType.label,
				type: 'animalType',
				eventType:
					animalType.eventType ||
					(typeof animalType.value === 'number' ? animalType.value : undefined),
			});
		});

	return options;
});

// 过滤后的选项列表
const filteredOptions = computed(() => {
	if (!filterKeyword.value || filterKeyword.value.trim().length === 0) {
		return allOptions.value;
	}

	const keyword = filterKeyword.value.trim().toLowerCase();
	return allOptions.value.filter((option) => {
		// 按名称匹配
		const nameMatch = option.label.toLowerCase().includes(keyword);
		// 按别名匹配（如果有）
		const aliasMatch = option.alias && option.alias.toLowerCase().includes(keyword);

		return nameMatch || aliasMatch;
	});
});

// 智能显示选项：初始状态只显示动物类型，有输入时显示过滤结果
const displayOptions = computed(() => {
	if (!hasInput.value) {
		// 没有输入时，只显示动物类型选项
		return animalTypesStore.animalTypeOptions
			.filter((item) => item.label !== '全部')
			.map((animalType) => ({
				label: animalType.label,
				value: animalType.label,
				type: 'animalType',
				eventType:
					animalType.eventType ||
					(typeof animalType.value === 'number' ? animalType.value : undefined),
			}));
	} else {
		// 有输入时，显示过滤后的完整选项列表
		return filteredOptions.value;
	}
});

// 本地过滤方法
const handleLocalFilter = (value: string) => {
	filterKeyword.value = value;
	// 更新输入状态
	hasInput.value = !!(value && value.trim().length > 0);
};

// 处理物种选择
const handleSpeciesChange = (selectedName: string, index: number) => {
	if (!selectedName) {
		// 清空选择
		state.MEResult[index].name = '';
		state.MEResult[index].eventType = undefined;
	} else {
		// 从统一选项列表中查找选中的项
		const selectedOption = allOptions.value.find((option) => option.value === selectedName);

		if (selectedOption) {
			state.MEResult[index].name = selectedName;
			// 使用选项中的eventType，如果没有则使用原始监控事件的eventType
			state.MEResult[index].eventType = selectedOption.eventType || state.ME.eventType;
		} else {
			// 兜底处理：如果在选项中找不到，直接设置名称
			state.MEResult[index].name = selectedName;
			state.MEResult[index].eventType = state.ME.eventType;
		}
	}
	// 重新绘制
	drawImageAndResult();
};

const state = reactive({
	dialog: {
		isShowDialog: false,
		title: '修改识别结果',
	},
	ME: {} as MonitorEventRow, // 要修改的识别结果
	MEResult: [] as MEResult[],
	activeResult: -1,
	imageLoading: false, // 图片加载状态
});

const openDialog = (ME: MonitorEventRow) => {
	state.ME = ME;
	onResetMEResult(false); // 获取识别结果
	state.activeResult = -1;

	// 立即显示弹窗，不等待数据加载
	state.dialog.isShowDialog = true;
	window.addEventListener('resize', onResizeEvent);
	window.addEventListener('keydown', onKeydownEvent);

	// 异步预加载数据，不阻塞弹窗显示
	loadDataAsync();

	nextTick(() => {
		initCanvas();
	});
};

// 异步加载数据方法
const loadDataAsync = async () => {
	try {
		dataLoading.value = true;

		// 并行加载物种数据和动物类型数据
		await Promise.all([ensureSpeciesData(), animalTypesStore.ensureAnimalTypes()]);

		console.log('弹窗数据加载完成');
	} catch (error) {
		console.error('弹窗数据加载失败:', error);
		// 即使数据加载失败，也不影响弹窗的基本使用
	} finally {
		dataLoading.value = false;
	}
};
const closeDialog = (done?: () => void) => {
	state.dialog.isShowDialog = false;
	window.removeEventListener('resize', onResizeEvent);
	window.removeEventListener('keydown', onKeydownEvent);
	if (done) done();
};

defineExpose({
	openDialog,
});

const onResizeEvent = () => {
	initCanvas();
};

import { useExistSpeciesStore } from '/@/stores/existSpecies';
const existSpeciesStore = useExistSpeciesStore();
// 确定
const onConfirm = async () => {
	const noEmpty = state.MEResult.every((item) => item.name && item.count);
	if (!noEmpty) {
		ElMessage.error('物种名称及数量不能为空');
		return;
	}
	const list: MEResult[] = [];
	const monitorEventDetails: MonitorEventDetail[] = [];
	// 收集所有物种的eventType，用于确定最终的eventType
	const eventTypes = new Set<number>();

	state.MEResult.forEach((item: MEResult) => {
		// 收集eventType
		if (item.eventType !== undefined) {
			eventTypes.add(item.eventType);
		}

		list.push({
			name: item.name,
			// bbox必须为整数型，不然会导致生成的新图结果框位置有误
			bbox: [
				Number(item.bbox[0].toFixed(0)),
				Number(item.bbox[1].toFixed(0)),
				Number(item.bbox[2].toFixed(0)),
				Number(item.bbox[3].toFixed(0)),
			],
			score: item.score,
			count: item.count,
			eventType: item.eventType,
		});
		const index = monitorEventDetails.findIndex(
			(m) => m.recResult === item.name && m.eventType === item.eventType
		);
		if (index !== -1) {
			monitorEventDetails[index].recResultCnt += item.count;
		} else {
			monitorEventDetails.push({
				recResult: item.name,
				recResultCnt: item.count,
				eventType: item.eventType,
			});
		}
	});

	// 确定最终的eventType：
	// 1. 如果选择的是"人"，硬编码eventType为0
	// 2. 如果所有物种都有相同的eventType，使用该eventType
	// 3. 如果有多个不同的eventType，使用原始监控事件的eventType
	// 4. 如果没有物种eventType，使用原始监控事件的eventType
	let finalEventType = state.ME.eventType;

	// 检查是否选择了"人"
	const hasHuman = state.MEResult.some((item: MEResult) => item.name === '人');
	if (hasHuman) {
		finalEventType = 0;
	} else if (eventTypes.size === 1) {
		finalEventType = Array.from(eventTypes)[0];
	}

	const data = {
		id: state.ME.id,
		oriPictureId: state.ME.oriPictureId,
		rawResult: JSON.stringify({ list }),
		monitorEventDetails,
		eventType: finalEventType,
	};
	await updateMonitorEvent(data);
	// 通过mitt事件总线通知其他组件刷新
	mittBus.emit('monitorEventRefresh');
	existSpeciesStore.ElMessage.success('识别结果修改成功');
	closeDialog();
};
// 重置
const onResetMEResult = (isDraw: boolean) => {
	const { rawResult } = state.ME;
	// 未识别到物种
	if (!rawResult) {
		state.MEResult = [];
	} else {
		/**
		 * 识别到物种
		 * monitorEventDetails：单个物种及总数量
		 * rawResult.list：单个物种名称、数量及位置，数量默认为1；
		 */
		const parseRawResult = JSON.parse(rawResult);
		if (parseRawResult && parseRawResult.list && parseRawResult.list.length > 0) {
			state.MEResult = parseRawResult.list.map((item: any) => ({
				...item,
				count: item.count || 1,
			}));
		} else {
			state.MEResult = [];
		}
	}
	if (isDraw) drawImageAndResult();
};
// 删除
const onDelete = ($index: number) => {
	state.MEResult.splice($index, 1);
	drawImageAndResult();
	ElMessage.warning('删除成功，点击确定保存修改记录');
	state.activeResult = -1;
};
const onKeydownEvent = (event: KeyboardEvent) => {
	const key = event.key;
	if (key === 'Delete') {
		if (state.activeResult === -1) {
			ElMessage.error('请先选择要删除的物种');
			return;
		}
		onDelete(state.activeResult);
	}
};

const canvasRef = ref<HTMLCanvasElement>();
let ctx: CanvasRenderingContext2D;
let canvasWidth: number; // canvas宽
let canvasHeight: number; // canvas高
let imageWidth: number; // 原图宽
let imageHeight: number; // 原图高
let imageEle: HTMLImageElement;
let imageScale: number; // 原图缩放比
let imageChange = {
	icw: 0, // canvas图像宽
	ich: 0, // canvas图像高
	x: 0, // canvas图像距画布左上角 x轴距离
	y: 0, // canvas图像距画布左上角 y轴距离
	scale: 1, // 图片缩放值
	minScale: 1, // 最小缩放值
	maxScale: 100, // 最大缩放值
};
const borderWidth = 1; // 标注框宽
const dotRadius = 4; // 边框四角圆点半径
const initCanvas = () => {
	ctx = <CanvasRenderingContext2D>canvasRef.value?.getContext('2d');
	canvasWidth = (<HTMLCanvasElement>canvasRef.value).width = <number>canvasRef.value?.clientWidth;
	canvasHeight = (<HTMLCanvasElement>canvasRef.value).height = <number>(
		canvasRef.value?.clientHeight
	);
	initImage();
};
// 加载图片
const initImage = () => {
	state.imageLoading = true; // 开始加载图片，显示loading
	imageEle = new Image();
	imageEle.onload = () => {
		imageWidth = imageEle.width;
		imageHeight = imageEle.height;
		let dx = 0;
		let dy = 0;
		let dw = canvasWidth;
		let dh = canvasHeight;
		if (imageWidth / canvasWidth < imageHeight / canvasHeight) {
			dw = (imageWidth * canvasHeight) / imageHeight;
			dx = (canvasWidth - dw) / 2;
		} else {
			dh = (imageHeight * canvasWidth) / imageWidth;
			dy += dy + (canvasHeight - dh) / 2;
		}
		imageChange = Object.assign(imageChange, {
			x: dx,
			y: dy,
			icw: dw,
			ich: dh,
		});
		imageScale = dw / imageWidth;
		drawImageAndResult();
		state.imageLoading = false; // 图片加载并绘制完成，隐藏loading
	};
	imageEle.src = state.ME.oriPictureUrl;
};
// 绘制图片及识别结果框
const drawImageAndResult = () => {
	console.log('=== drawImageAndResult START ===');
	console.log('当前 activeResult:', state.activeResult);
	console.log('MEResult 数量:', state.MEResult.length);

	ctx.clearRect(0, 0, canvasWidth, canvasHeight);
	const { x, y, icw, ich, scale } = imageChange;
	// 绘制图片
	ctx.drawImage(imageEle, 0, 0, imageWidth, imageHeight, x, y, icw * scale, ich * scale);
	// 绘制识别结果
	state.MEResult.forEach((item: MEResult, $index: number) => {
		console.log(`绘制标注框 ${$index}:`, {
			isActive: state.activeResult === $index,
			activeResult: state.activeResult,
			currentIndex: $index,
		});

		if (!item.bbox) return;
		const minx = (item.minx = item.bbox[0] * imageScale * scale);
		const miny = (item.miny = item.bbox[1] * imageScale * scale);
		const maxx = (item.maxx = item.bbox[2] * imageScale * scale);
		const maxy = (item.maxy = item.bbox[3] * imageScale * scale);
		if (state.activeResult === $index) {
			console.log(`标注框 ${$index} 是活动状态，绘制选中效果`);
		} else {
			console.log(`标注框 ${$index} 不是活动状态`);
		}
		if (state.activeResult === $index) {
			// 标注框四角圆点（只有添加标注框才存在四角圆点）
			ctx.fillStyle = 'red';
			// 左上角dot
			ctx.beginPath();
			ctx.arc(minx + x, miny + y, dotRadius, 0, Math.PI * 2);
			ctx.fill();
			// 右上角dot
			ctx.beginPath();
			ctx.arc(maxx + x, miny + y, dotRadius, 0, Math.PI * 2);
			ctx.fill();
			// 右下角dot
			ctx.beginPath();
			ctx.arc(maxx + x, maxy + y, dotRadius, 0, Math.PI * 2);
			ctx.fill();
			// 左下角dot
			ctx.beginPath();
			ctx.arc(minx + x, maxy + y, dotRadius, 0, Math.PI * 2);
			ctx.fill();
			// 背景
			ctx.beginPath();
			ctx.fillStyle = 'rgba(102,102,102,0.3)';
			ctx.fillRect(minx + x, miny + y, maxx - minx, maxy - miny);
		}
		// 绘制边框
		ctx.beginPath();
		ctx.lineWidth = borderWidth;
		ctx.strokeStyle = 'red';
		ctx.setLineDash(state.activeResult === $index ? [4, 4] : []); // 虚实线边框
		ctx.strokeRect(minx + x, miny + y, maxx - minx, maxy - miny);
		// 绘制文字
		ctx.fillStyle = 'red';
		ctx.fillRect(minx + x - borderWidth / 2, miny + y - 20, maxx - minx + borderWidth, 20);
		ctx.font = '14px serif';
		ctx.fillStyle = '#fff';
		ctx.fillText(item.name, minx + x, miny + y - 5);
	});
};
// 鼠标滚轮放大缩小
const onWheelEvent = (event: WheelEvent) => {
	event.preventDefault();
	const { deltaY } = event;
	const { scale, minScale, maxScale } = imageChange;
	const radio = deltaY * 0.001;
	let newScale = Number((scale - radio).toFixed(2));
	if (newScale > maxScale) {
		newScale = maxScale;
	}
	if (newScale < minScale) {
		newScale = minScale;
	}
	imageChange.scale = newScale;
	// 以鼠标位置缩放
	const { offsetX, offsetY } = mouseToImagePos(event.offsetX, event.offsetY);
	imageChange.x -= (newScale - scale) * (offsetX / scale);
	imageChange.y -= (newScale - scale) * (offsetY / scale);
	drawImageAndResult();
};
// 鼠标按下
const onMousedownEvent = (event: MouseEvent) => {
	event.preventDefault();
	let startX = event.offsetX;
	let startY = event.offsetY;

	const { x, y, scale } = imageChange;
	const isDraggleImage = event.button === 2; // 情况1：鼠标右键拖拽图片
	// 鼠标左键按下时所处的位置，mClickPos：0其他区域 1标记框左上角 2标记框右上角 3标记框右下角 4标记框左下角 5标记框内（除四角）
	let mClickPos: number;
	let oldTag: MEResult;
	let isAddNew = false;

	if (!isDraggleImage) {
		// 情况2：鼠标左键点击标记框，保存当前信息
		mClickPos = mouseClickPos(event.offsetX, event.offsetY);
		if (mClickPos !== 0) {
			oldTag = { ...state.MEResult[state.activeResult] };
		} else {
			// 情况3：鼠标左键点击其他区域 ，添加标注
			state.activeResult = -1;
		}
		drawImageAndResult();
	}
	// 修改标注边界处理
	const moveBoundary = (ev: MouseEvent, newBbox: number[]) => {
		if (newBbox[0] >= newBbox[2] && newBbox[1] >= newBbox[3]) {
			startX = ev.offsetX;
			startY = ev.offsetY;
			oldTag.bbox = state.MEResult[state.activeResult].bbox;
			mClickPos = mClickPos === 1 ? 3 : mClickPos === 2 ? 4 : mClickPos === 3 ? 1 : 2;
			return true;
		}
		if (newBbox[0] >= newBbox[2]) {
			startX = ev.offsetX;
			startY = ev.offsetY;
			oldTag.bbox = state.MEResult[state.activeResult].bbox;
			mClickPos = mClickPos === 1 ? 2 : mClickPos === 2 ? 1 : mClickPos === 3 ? 4 : 3;
			return true;
		}
		if (newBbox[1] >= newBbox[3]) {
			startX = ev.offsetX;
			startY = ev.offsetY;
			oldTag.bbox = state.MEResult[state.activeResult].bbox;
			mClickPos = mClickPos === 1 ? 4 : mClickPos === 2 ? 3 : mClickPos === 3 ? 2 : 1;
			return true;
		}
		return false;
	};

	const mousemoveEvent = (ev: MouseEvent) => {
		// 鼠标右键拖拽图片
		if (isDraggleImage) {
			const moveX = ev.offsetX - startX;
			const moveY = ev.offsetY - startY;
			imageChange.x = x + moveX;
			imageChange.y = y + moveY;
			drawImageAndResult();
			return;
		}
		// 添加标注
		if (mClickPos === 0) {
			(<HTMLCanvasElement>canvasRef.value).style.cursor = 'crosshair';
			const mousePos = mouseToImagePos(startX, startY);
			const startPointX = mousePos.offsetX / scale / imageScale;
			const startPointY = mousePos.offsetY / scale / imageScale;
			const moveX = (ev.offsetX - startX) / scale / imageScale;
			const moveY = (ev.offsetY - startY) / scale / imageScale;
			// 移动距离小于10忽略
			if (Math.abs(moveX) < 10 || Math.abs(moveY) < 10) return;
			if (!isAddNew) {
				state.MEResult.push({ name: '', bbox: [], score: 1, count: 1 });
				isAddNew = true;
			}
			state.activeResult = state.MEResult.length - 1;
			state.MEResult[state.activeResult].bbox = [
				moveX > 0 ? startPointX : startPointX + moveX,
				moveY > 0 ? startPointY : startPointY + moveY,
				moveX > 0 ? startPointX + moveX : startPointX,
				moveY > 0 ? startPointY + moveY : startPointY,
			];
			drawImageAndResult();
			return;
		}
		// 修改标注
		const moveX = (ev.offsetX - startX) / scale / imageScale;
		const moveY = (ev.offsetY - startY) / scale / imageScale;
		switch (mClickPos) {
			case 1:
				(<HTMLCanvasElement>canvasRef.value).style.cursor = 'nw-resize';
				const newBbox1 = [
					oldTag.bbox[0] + moveX,
					oldTag.bbox[1] + moveY,
					oldTag.bbox[2],
					oldTag.bbox[3],
				];
				if (moveBoundary(ev, newBbox1)) return;
				state.MEResult[state.activeResult].bbox = newBbox1;
				break;
			case 2:
				(<HTMLCanvasElement>canvasRef.value).style.cursor = 'ne-resize';
				const newBbox2 = [
					oldTag.bbox[0],
					oldTag.bbox[1] + moveY,
					oldTag.bbox[2] + moveX,
					oldTag.bbox[3],
				];
				if (moveBoundary(ev, newBbox2)) return;
				state.MEResult[state.activeResult].bbox = newBbox2;
				break;
			case 3:
				(<HTMLCanvasElement>canvasRef.value).style.cursor = 'se-resize';
				const newBbox3 = [
					oldTag.bbox[0],
					oldTag.bbox[1],
					oldTag.bbox[2] + moveX,
					oldTag.bbox[3] + moveY,
				];
				if (moveBoundary(ev, newBbox3)) return;
				state.MEResult[state.activeResult].bbox = newBbox3;
				break;
			case 4:
				(<HTMLCanvasElement>canvasRef.value).style.cursor = 'sw-resize';
				const newBbox4 = [
					oldTag.bbox[0] + moveX,
					oldTag.bbox[1],
					oldTag.bbox[2],
					oldTag.bbox[3] + moveY,
				];
				if (moveBoundary(ev, newBbox4)) return;
				state.MEResult[state.activeResult].bbox = newBbox4;
				break;
			case 5:
				(<HTMLCanvasElement>canvasRef.value).style.cursor = 'move';
				state.MEResult[state.activeResult].bbox = [
					oldTag.bbox[0] + moveX,
					oldTag.bbox[1] + moveY,
					oldTag.bbox[2] + moveX,
					oldTag.bbox[3] + moveY,
				];
				break;
			default:
				break;
		}
		drawImageAndResult();
	};
	const mouseupEvent = () => {
		(<HTMLCanvasElement>canvasRef.value).style.cursor = 'default';
		(<HTMLCanvasElement>canvasRef.value).removeEventListener('mousemove', mousemoveEvent);
		(<HTMLCanvasElement>canvasRef.value).removeEventListener('mouseup', mouseupEvent);
	};
	(<HTMLCanvasElement>canvasRef.value).addEventListener('mousemove', mousemoveEvent);
	(<HTMLCanvasElement>canvasRef.value).addEventListener('mouseup', mouseupEvent);
};
/**
 * canvas内，鼠标相对于canvas图像的坐标
 * @param {Number} mouseX 鼠标距离canvas左上角 x轴坐标
 * @param {Number} mouseY 鼠标距离canvas左上角 y轴坐标
 * @returns {object} offsetX offsetY（canvas内鼠标距离图像左上角 x/y轴坐标）
 */
const mouseToImagePos = (mouseX: number, mouseY: number) => {
	const { x, y } = imageChange;
	return {
		offsetX: mouseX - x,
		offsetY: mouseY - y,
	};
};
// 判断一个点是否在矩形区域中
const dotInRect = (
	coord: { x: number; y: number },
	minCoord: { x: number; y: number },
	maxCoord: { x: number; y: number }
) => {
	return (
		coord.x >= minCoord.x && coord.y >= minCoord.y && coord.x <= maxCoord.x && coord.y <= maxCoord.y
	);
};
/**
 * 鼠标左键按下时所处位置
 * @param {Number} mouseX canvas内鼠标距离canvas左上角 x轴坐标
 * @param {Number} mouseY canvas内鼠标距离canvas左上角 y轴坐标
 * @return {Number} 0canvas其他区域 1激活框左上角 2激活框右上角 3激活框右下角 4激活框左下角 5激活框其他区域
 */
const mouseClickPos = (mouseX: number, mouseY: number) => {
	const { offsetX, offsetY } = mouseToImagePos(mouseX, mouseY);
	const index = state.MEResult.findIndex((item: MEResult) => {
		return dotInRect(
			{ x: offsetX, y: offsetY },
			{ x: <number>item.minx - dotRadius, y: <number>item.miny - dotRadius },
			{ x: <number>item.maxx + dotRadius, y: <number>item.maxy + dotRadius }
		);
	});
	if (index != -1) {
		state.activeResult = index;
		const item = state.MEResult[index];
		if (
			dotInRect(
				{ x: offsetX, y: offsetY },
				{ x: <number>item.minx - dotRadius, y: <number>item.miny - dotRadius },
				{ x: <number>item.minx + dotRadius, y: <number>item.miny + dotRadius }
			)
		)
			return 1;
		if (
			dotInRect(
				{ x: offsetX, y: offsetY },
				{ x: <number>item.maxx - dotRadius, y: <number>item.miny - dotRadius },
				{ x: <number>item.maxx + dotRadius, y: <number>item.miny + dotRadius }
			)
		)
			return 2;
		if (
			dotInRect(
				{ x: offsetX, y: offsetY },
				{ x: <number>item.maxx - dotRadius, y: <number>item.maxy - dotRadius },
				{ x: <number>item.maxx + dotRadius, y: <number>item.maxy + dotRadius }
			)
		)
			return 3;
		if (
			dotInRect(
				{ x: offsetX, y: offsetY },
				{ x: <number>item.minx - dotRadius, y: <number>item.maxy - dotRadius },
				{ x: <number>item.minx + dotRadius, y: <number>item.maxy + dotRadius }
			)
		)
			return 4;
		return 5;
	}
	return 0;
};
</script>

<style lang="scss" scoped>
:global(.ME-edit-dialog.el-dialog.is-fullscreen .el-dialog__header) {
	box-shadow: 0 1px 2px 0 var(--el-input-border-color, var(--el-border-color));
	margin-right: 0;
}
:global(.ME-edit-dialog.el-dialog.is-fullscreen .el-dialog__body) {
	height: calc(100% - 54px);
	max-height: calc(100% - 54px) !important;
	overflow: hidden;
	padding-right: 0 !important;
}
.ME-container {
	height: 100%;
	display: flex;
	flex-direction: row;
	.ME-center-canvas {
		min-width: 0;
		flex: 1;
		height: 100%;
		display: flex;
		flex-direction: column;
		margin-right: 20px;
		.canvas-container {
			flex: 1;
			min-height: 0;
			position: relative;
			box-shadow: 0 0 0 1px var(--el-input-border-color, var(--el-border-color)) inset;
			#canvasId {
				width: 100%;
				height: 100%;
			}
		}
	}
	.ME-right-result {
		width: 360px;
		height: 100%;
		overflow-y: auto;
	}
}

@keyframes rotating {
	0% {
		transform: rotate(0deg);
	}
	100% {
		transform: rotate(360deg);
	}
}
</style>
